@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');
@import './variables.css';

/* Reset & Base Styles */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--space-4);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-md);
}

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
}

a {
  color: var(--primary-500);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-600);
}

img {
  max-width: 100%;
  height: auto;
}

code {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  padding: var(--space-1) var(--space-2);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.section {
  padding: var(--space-12) 0;
}

.section-title {
  position: relative;
  margin-bottom: var(--space-8);
}

.section-title.text-center {
  text-align: center;
}

.section-title.text-center::after {
  left: 50%;
  transform: translateX(-50%);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #bb86fc, #03dac6);
  border-radius: var(--radius-full);
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: var(--space-16);
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(var(--primary-400), 0.1), transparent 70%),
              radial-gradient(circle at bottom left, rgba(var(--secondary-400), 0.1), transparent 70%);
  z-index: 0;
}

.hero-section > * {
  position: relative;
  z-index: 1;
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: transparent;
}

.card-body {
  padding: var(--space-5);
}

.card-title {
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
}

.card-text {
  color: var(--text-secondary);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-medium);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-md);
  line-height: 1.5;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
  z-index: -1;
}

.btn:hover::after {
  transform: translateX(0);
}

.btn-primary {
  color: white;
  background: var(--gradient-primary);
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.btn-outline {
  color: var(--primary-500);
  background-color: transparent;
  border: 2px solid var(--primary-500);
  box-shadow: none;
}

.btn-outline:hover {
  color: white;
  background-color: var(--primary-500);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  color: white;
  background: var(--gradient-secondary);
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.btn-lg {
  padding: var(--space-3) var(--space-5);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-lg);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out forwards;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Utilities */
.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-primary {
  color: #bb86fc !important;
}

.text-secondary {
  color: #03dac6 !important;
}

.text-accent {
  color: #cf6679 !important;
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

.bg-gradient-cool {
  background: var(--gradient-cool);
}

.bg-gradient-warm {
  background: var(--gradient-warm);
}

.bg-gradient-dark {
  background: var(--gradient-dark);
}

.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-tertiary {
  background-color: var(--bg-tertiary);
}

.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

.rounded-sm {
  border-radius: var(--radius-sm);
}

.rounded {
  border-radius: var(--radius-lg);
}

.rounded-lg {
  border-radius: var(--radius-xl);
}

.rounded-full {
  border-radius: var(--radius-full);
}

.border {
  border: 1px solid var(--border-color);
}

.border-primary {
  border: 1px solid var(--md-purple);
}

.border-secondary {
  border: 1px solid var(--md-teal);
}

.backdrop-blur {
  backdrop-filter: blur(10px);
}

.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }

.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }

/* Responsive Design - Mobile First Approach */

/* Large tablets and small desktops */
@media (max-width: 992px) {
  .container {
    padding: 0 var(--space-3);
  }

  .section {
    padding: var(--space-10) 0;
  }

  .hero-section {
    padding-top: var(--space-12);
  }

  .btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
  }

  .btn-lg {
    padding: var(--space-3) var(--space-5);
    font-size: var(--font-size-md);
  }
}

/* Tablets */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  .container {
    padding: 0 var(--space-3);
  }

  .section {
    padding: var(--space-8) 0;
  }

  .hero-section {
    padding-top: var(--space-10);
    min-height: 90vh;
  }

  .section-title {
    font-size: 2rem;
  }

  .card {
    margin-bottom: var(--space-4);
  }

  .btn {
    width: 100%;
    margin-bottom: var(--space-2);
  }

  .btn:last-child {
    margin-bottom: 0;
  }
}

/* Mobile devices */
@media (max-width: 576px) {
  html {
    font-size: 13px;
  }

  .container {
    padding: 0 var(--space-2);
  }

  .section {
    padding: var(--space-6) 0;
  }

  .hero-section {
    padding-top: var(--space-8);
    min-height: 85vh;
  }

  .section-title {
    font-size: 1.75rem;
    margin-bottom: var(--space-6);
  }

  .card-body {
    padding: var(--space-4);
  }

  .btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
  }

  .btn-lg {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-md);
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  html {
    font-size: 12px;
  }

  .container {
    padding: 0 var(--space-2);
  }

  .section {
    padding: var(--space-5) 0;
  }

  .hero-section {
    padding-top: var(--space-6);
    min-height: 80vh;
  }

  .section-title {
    font-size: 1.5rem;
    margin-bottom: var(--space-5);
  }

  .card {
    border-radius: var(--radius-md);
  }

  .card-body {
    padding: var(--space-3);
  }

  .btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-sm);
  }

  .btn-lg {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px; /* Apple's recommended touch target size */
    min-width: 44px;
  }

  .nav-link {
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .card:hover {
    transform: none; /* Disable hover effects on touch devices */
  }

  .btn:hover {
    transform: none;
  }
}
