:root {
  /* Primary Colors - Purple Theme */
  --primary-50: #f3f0ff;
  --primary-100: #e9e2ff;
  --primary-200: #d6c9ff;
  --primary-300: #bfa6ff;
  --primary-400: #a67dff;
  --primary-500: #bb86fc;
  --primary-500-rgb: 187, 134, 252;
  --primary-600: #9a66ea;
  --primary-700: #7c4ddb;
  --primary-800: #6b3bc7;
  --primary-900: #5a2fb3;
  --primary-900-rgb: 90, 47, 179;

  /* Secondary Colors - Gray */
  --secondary-50: #f9fafb;
  --secondary-100: #f3f4f6;
  --secondary-200: #e5e7eb;
  --secondary-300: #d1d5db;
  --secondary-400: #9ca3af;
  --secondary-500: #6b7280;
  --secondary-600: #4b5563;
  --secondary-700: #374151;
  --secondary-800: #1f2937;
  --secondary-900: #111827;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  /* Accent Colors */
  --accent-purple: #8b5cf6;
  --accent-pink: #ec4899;
  --accent-orange: #f97316;
  --accent-teal: #14b8a6;
  --accent-green: #10b981;
  --accent-yellow: #fbbf24;
  --accent-red: #ef4444;

  /* Semantic Colors */
  --success: var(--secondary-500);
  --info: var(--primary-500);
  --warning: var(--accent-orange);
  --danger: #dc3545;

  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-md: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  --font-size-5xl: 3rem;     /* 48px */
  --font-size-6xl: 3.75rem;  /* 60px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Spacing */
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.5rem;   /* 24px */
  --space-6: 2rem;     /* 32px */
  --space-8: 3rem;     /* 48px */
  --space-10: 4rem;    /* 64px */
  --space-12: 6rem;    /* 96px */
  --space-16: 8rem;    /* 128px */

  /* Border Radius */
  --radius-sm: 0.125rem;  /* 2px */
  --radius-md: 0.25rem;   /* 4px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* Z-index */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;

  /* Dark Theme (Default) - Modern Material Design Inspired */
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #252525;
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-tertiary: #a0a0a0;
  --border-color: #333333;
  --card-bg: rgba(30, 30, 30, 0.7);
  --navbar-bg: rgba(18, 18, 18, 0.9);
  --navbar-text-color: var(--text-primary); /* Added for clarity */
  --footer-bg: #0a0a0a;

  /* Material Design Colors */
  --md-purple: #bb86fc;
  --md-purple-variant: #9a66ea;
  --md-teal: #03dac6;
  --md-teal-variant: #00a896;
  --md-red: #cf6679;
  --md-red-variant: #b55464;
  --md-blue: #64b5f6;
  --md-blue-variant: #4a86c5;
  --md-amber: #ffb74d;
  --md-amber-variant: #d69a41;

  /* Dark Theme Gradients */
  --gradient-primary: linear-gradient(135deg, var(--md-purple), var(--md-teal));
  --gradient-secondary: linear-gradient(135deg, var(--md-blue), var(--md-purple-variant));
  --gradient-accent: linear-gradient(135deg, var(--md-red), var(--md-purple));
  --gradient-cool: linear-gradient(135deg, var(--md-teal), var(--md-blue));
  --gradient-warm: linear-gradient(135deg, var(--md-amber), var(--md-red));
  --gradient-dark: linear-gradient(135deg, #121212, #252525);
  --card-shadow: 0 10px 20px -3px rgba(0, 0, 0, 0.7), 0 4px 6px -2px rgba(0, 0, 0, 0.5);

  /* Chatbot specific variables */
  --chatbot-primary-color: var(--md-purple); /* Bot message color / primary chat action */
  --chatbot-user-message-bg: var(--md-teal-variant); /* New: User message background */
  --chatbot-primary-hover-color: var(--md-purple-variant);
  --chatbot-input-bg: #2a2a2a;
  --chatbot-placeholder-color: var(--text-tertiary);
  --chatbot-bot-message-bg: var(--md-blue-variant); /* New: Lighter Bot message background */

  /* Scrollbar variables */
  --scrollbar-track-bg: var(--bg-secondary);
  --scrollbar-thumb-bg: var(--neutral-700);
  --scrollbar-thumb-hover-bg: var(--neutral-600);
}

/* Light Theme - Not Used */
[data-theme="light"] {
  --bg-primary: var(--neutral-50);
  --bg-secondary: var(--neutral-100);
  --bg-tertiary: var(--neutral-200);
  --text-primary: var(--primary-900);
  --text-secondary: var(--primary-700);
  --text-tertiary: var(--primary-500);
  --border-color: var(--neutral-200);
  --card-bg: white;
  --navbar-bg: rgba(255, 255, 255, 0.9);
  --footer-bg: var(--neutral-100);

  /* Light Theme Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-700), var(--primary-900));
  --gradient-secondary: linear-gradient(135deg, var(--secondary-600), var(--secondary-800));
  --gradient-accent: linear-gradient(135deg, var(--primary-800), var(--accent-purple));
  --gradient-cool: linear-gradient(135deg, var(--accent-teal), var(--primary-800));
  --gradient-warm: linear-gradient(135deg, var(--accent-orange), var(--accent-pink));
  --card-shadow: 0 10px 20px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
