/* Date Time Banner */
.datetime-banner {
  background: linear-gradient(135deg, #bb86fc, #03dac6) !important;
  padding: 0.4rem 0 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 1040 !important; /* Higher than navbar but not excessive */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
  display: block !important;
  visibility: visible !important;
}

.datetime-content {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 1.5rem !important;
  color: black !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
}

.current-date {
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.current-time {
  font-size: 0.85rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.2rem 0.6rem;
  border-radius: 15px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  color: black;
}

.current-timezone {
  font-size: 0.9rem;
  font-weight: 600;
  color: black;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .datetime-banner {
    padding: 0.3rem 0 !important;
  }

  .datetime-content {
    flex-direction: row !important; /* Keep horizontal on mobile for better space usage */
    gap: 0.5rem !important;
    font-size: 0.75rem !important;
    flex-wrap: wrap;
    justify-content: center !important;
  }

  .current-date {
    font-size: 0.7rem !important;
  }

  .current-time {
    font-size: 0.7rem !important;
    padding: 0.1rem 0.4rem !important;
  }

  .current-timezone {
    font-size: 0.7rem !important;
  }
}

/* Very small mobile devices */
@media (max-width: 480px) {
  .datetime-banner {
    padding: 0.25rem 0 !important;
  }

  .datetime-content {
    flex-direction: column !important;
    gap: 0.2rem !important;
    font-size: 0.7rem !important;
  }

  .current-date {
    font-size: 0.65rem !important;
  }

  .current-time {
    font-size: 0.65rem !important;
    padding: 0.1rem 0.3rem !important;
  }

  .current-timezone {
    font-size: 0.65rem !important;
  }
}
