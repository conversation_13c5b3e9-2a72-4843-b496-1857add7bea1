import React, { useState, useEffect } from 'react';
import { Navbar, Nav, Container } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import { FaCode } from 'react-icons/fa';
import './Header.css';
import Chatbot from './Chatbot'; // Re-import the Chatbot component

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  const [showChat, setShowChat] = useState(false); // State for chat visibility
  const [expanded, setExpanded] = useState(false); // State for navbar collapse
  const location = useLocation();

  const toggleChat = () => setShowChat(!showChat); // Function to toggle chat

  // Function to handle navigation clicks and close mobile menu
  const handleNavClick = (path) => {
    console.log('Navigation clicked:', path);
    setExpanded(false); // Close the mobile navbar when a link is clicked
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <>
      <Navbar
        expand="lg"
        fixed="top"
        expanded={expanded}
        onToggle={setExpanded}
        className={`modern-navbar ${scrolled ? 'scrolled' : ''}`}
      >
        <Container>
          <Navbar.Brand as={Link} to="/" className="brand">
            <FaCode className="brand-icon" />
            <span className="brand-name">Rahul Bhoyar</span>
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="ms-auto">
              <Nav.Link
                as={Link}
                to="/"
                className={location.pathname === '/' ? 'active' : ''}
                onClick={() => handleNavClick('/')}
              >
                Home
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/about"
                className={location.pathname === '/about' ? 'active' : ''}
                onClick={() => handleNavClick('/about')}
              >
                About
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/projects"
                className={location.pathname === '/projects' ? 'active' : ''}
                onClick={() => handleNavClick('/projects')}
              >
                Projects
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/publications"
                className={location.pathname === '/publications' ? 'active' : ''}
                onClick={() => handleNavClick('/publications')}
              >
                Publications
              </Nav.Link>
              <Nav.Link
                as={Link}
                to="/certifications"
                className={location.pathname === '/certifications' ? 'active' : ''}
                onClick={() => handleNavClick('/certifications')}
              >
                Certifications
              </Nav.Link>
              {/* <Nav.Link
                as={Link}
                to="/blog"
                className={location.pathname === '/blog' ? 'active' : ''}
                onClick={() => handleNavClick('/blog')}
              >
                Blog
              </Nav.Link> */}
              <Nav.Link
                as={Link}
                to="/contact"
                className={location.pathname === '/contact' ? 'active' : ''}
                onClick={() => handleNavClick('/contact')}
              >
                Contact
              </Nav.Link>
              {/* <button
                onClick={toggleChat}
                className="nav-link" // Use nav-link class for styling
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  color: 'var(--text-primary)', // Ensure text color matches
                  padding: '0.5rem 0.75rem', // Match Nav.Link padding
                  fontFamily: 'inherit',
                  fontSize: 'inherit',
                  lineHeight: 'inherit',
                  textAlign: 'center',
                  display: 'inline-block' // Ensure it behaves like other nav items
                }}
              >
                Chat With Me
              // </button> */}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>
      {/* Render Chatbot outside and sibling to Navbar */}
      {showChat && <Chatbot onClose={toggleChat} />}
    </>
  );
};

export default Header;
