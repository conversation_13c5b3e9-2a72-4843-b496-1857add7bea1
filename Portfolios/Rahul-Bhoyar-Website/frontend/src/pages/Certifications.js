import { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import axios from 'axios';
import './Certifications.css';

const Certifications = () => {
  const [certifications, setCertifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        const response = await axios.get('/api/certifications');
        setCertifications(response.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching certifications:', err);
        setError('Failed to load certifications. Please try again later.');
        setLoading(false);
      }
    };

    fetchCertifications();
  }, []);

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading certifications...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <div className="mt-5 pt-4">
      <section className="section">
        <Container>
          <h2 className="section-title text-center">Professional Certifications</h2>
          <div className="section-intro text-center mb-5">
            <p className="lead">
              Continuous professional development and industry-recognized credentials
            </p>
          </div>
          <Row className="justify-content-center">
            {certifications.map((cert) => (
              <Col md={4} key={cert.id} className="mb-4">
                <div className="certification-card text-left">
                  <div className="certification-logo">
                    {/* Display actual logos based on issuer */}
                    {cert.issuer.toLowerCase().includes('ibm') && (
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/51/IBM_logo.svg/1200px-IBM_logo.svg.png"
                        alt={`${cert.issuer} logo`}
                        className="cert-logo-img"
                      />
                    )}
                    {cert.issuer.toLowerCase().includes('stanford') && (
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b5/Seal_of_Leland_Stanford_Junior_University.svg/1200px-Seal_of_Leland_Stanford_Junior_University.svg.png"
                        alt={`${cert.issuer} logo`}
                        className="cert-logo-img"
                      />
                    )}
                    {cert.issuer.toLowerCase().includes('databricks') && (
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/63/Databricks_Logo.png/1200px-Databricks_Logo.png"
                        alt={`${cert.issuer} logo`}
                        className="cert-logo-img"
                      />
                    )}
                    {cert.issuer.toLowerCase().includes('google') && (
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/1200px-Google_2015_logo.svg.png"
                        alt={`${cert.issuer} logo`}
                        className="cert-logo-img"
                      />
                    )}
                    {cert.issuer.toLowerCase().includes('deeplearning') && (
                      <img
                        src="https://d3c33hcgiwev3.cloudfront.net/imageAssetProxy.v1/YXqb5XZXS_WQHJk6UQfQJA_f9e2798b8b8b4b8b8b8b8b8b8b8b8b8b_deeplearning-ai-logo.png?expiry=1640995200000&hmac=example"
                        alt={`${cert.issuer} logo`}
                        className="cert-logo-img"
                        onError={(e) => {
                          e.target.src = "https://via.placeholder.com/80x80/333/fff?text=DL.AI";
                        }}
                      />
                    )}
                    {cert.issuer.toLowerCase().includes('hackerrank') && (
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/65/HackerRank_logo.svg/1200px-HackerRank_logo.svg.png"
                        alt={`${cert.issuer} logo`}
                        className="cert-logo-img"
                      />
                    )}
                    {cert.issuer.toLowerCase().includes('github') && (
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/9/91/Octicons-mark-github.svg/1200px-Octicons-mark-github.svg.png"
                        alt={`${cert.issuer} logo`}
                        className="cert-logo-img"
                      />
                    )}
                    {/* Fallback for unknown issuers */}
                    {!['ibm', 'stanford', 'databricks', 'google', 'deeplearning', 'hackerrank', 'github'].some(issuer =>
                      cert.issuer.toLowerCase().includes(issuer)
                    ) && (
                      <span className="cert-icon">{cert.icon}</span>
                    )}
                  </div>
                  <h4 className="certification-title">{cert.title}</h4>
                  <p className="certification-issuer">{cert.issuer}</p>
                  <p className="certification-date">Issued: {cert.date}</p>
                  <div className="certification-verification">
                    {cert.verification_url ? (
                      <a
                        href={cert.verification_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="verification-link"
                      >
                        Verify Credential
                      </a>
                    ) : (
                      <span className="verification-unavailable">
                        Verification not available
                      </span>
                    )}
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default Certifications;
