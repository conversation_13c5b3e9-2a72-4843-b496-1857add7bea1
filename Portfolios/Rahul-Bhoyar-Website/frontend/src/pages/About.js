import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import axios from 'axios';
import SkillBar from '../components/SkillBar';
import './About.css';

const About = () => {
  const [skills, setSkills] = useState([]);
  const [experience, setExperience] = useState([]);
  const [education, setEducation] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fetch data from API
    const fetchData = async () => {
      try {
        const [skillsRes, expRes, eduRes] = await Promise.all([
          axios.get('/api/skills'),
          axios.get('/api/experience'),
          axios.get('/api/education')
        ]);

        setSkills(skillsRes.data);
        setExperience(expRes.data);
        setEducation(eduRes.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading about information...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <div className="mt-5 pt-4">
      <section className="section">
        <Container>
          <h2 className="section-title text-center">About Me</h2>
          <Row className="align-items-center mb-5">
            <Col lg={6} className="mb-4 mb-lg-0">
              <div className="about-content text-left">
                <h3 className="mb-3 text-gradient">AI and Software Professional</h3>
                <p className="lead">
                  I am an AI and Software Professional with over seven years of expertise in GenAI, AgenticAI, NLP, Deep Learning, Machine Learning, Data Science, and Web and APIs Development.
                </p>
                <p>
                  Currently, I work as an Research Associate/AI Engineer at the DFKI, Berlin (German Research Centre for Artificial Intelligence), actively contributing to transformative projects in education, research, e- commerce and healthcare for leading organizations.
                </p>
                <p>
                  My technical proficiency encompasses a wide range of tools and technologies, including Python, SQL, Django, Flask, FastAPI, GenAI and Agentic AI frameworks like LangChain, LangGraph, LlamaIndex, and cloud platforms such as AWS, GCP, and Microsoft Azure. Additionally, I am skilled in front-end technologies like HTML, CSS, and JavaScript, React, as well as Data Engineering and System Architecture.
                </p>

                <p>
                  Throughout my career, I have held diverse roles, including Python Full Stack Developer, Data Scientist/Engineer, Generative AI Engineer, and ML/AI Engineer. I bring strong expertise in System Design, Microservices, and both Agile (Scrum) and Waterfall business models. With a commitment to industry-standard programming practices, I consistently strive to stay at the forefront of technological advancements in software development.
                </p>
                <div className="about-stats">
                  <div className="stat-item">
                    <span className="stat-number counter">7+</span>
                    <span className="stat-label">Years Experience</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number counter">75+</span>
                    <span className="stat-label">Projects Delivered</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number counter">20+</span>
                    <span className="stat-label">Tech Stack Mastery</span>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={6}>
              <div className="about-card">
                <div className="about-card-header">
                  <div className="about-card-avatar">RB</div>
                  <div className="about-card-info">
                    <h3>Rahul Bhoyar</h3>
                    <p>AI and Software Professional</p>
                  </div>
                </div>
                <div className="about-card-body">
                  <div className="about-card-section text-left">
                    <h4>Core Technologies</h4>
                    <div className="tech-stack-icons">
                      <span className="tech-icon" title="Python">Py</span>
                      <span className="tech-icon" title="JavaScript">Js</span>
                      <span className="tech-icon" title="React">Re</span>
                      <span className="tech-icon" title="FastAPI">Fa</span>
                      <span className="tech-icon" title="Docker">Do</span>
                      <span className="tech-icon" title="TensorFlow">Tf</span>
                      <span className="tech-icon" title="PyTorch">Pt</span>
                      <span className="tech-icon" title="AWS">Aw</span>
                    </div>
                  </div>
                  <div className="about-card-section text-left">
                    <h4>Specializations</h4>
                    <p>Agentic AI, Generative AI, Deep Learning, Machine Learning, Data Science, Data Engineering, Backend and Frontend Development</p>
                  </div>
                  <div className="about-card-section text-left">
                    <h4>Languages</h4>
                    <p>English (C2), German (A2)</p>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section className="section bg-gradient-dark">
        <Container>
          <h2 className="section-title text-center">Technical Skills</h2>
          <div className="skills-intro text-center mb-5">
            <p className="lead">
              My expertise spans across multiple domains, with a focus on full-stack development and AI technologies
            </p>
          </div>
          <Row>
            {skills.map((skill, index) => (
              <Col md={6} key={index} className="mb-4">
                <SkillBar skill={skill} />
              </Col>
            ))}
          </Row>
          <div className="skills-categories mt-5">
            <Row>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-accent mb-3">🤖 Agentic AI and Generative AI</h4>
                  <ul className="skill-list">
                    <li>LangGraph</li>
                    <li>LangChain</li>
                    <li>Phidata</li>
                    <li>LlamaIndex</li>
                    <li>CrewAI</li>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-accent mb-3">🧠 Deep Learning and Machine Learning</h4>
                  <ul className="skill-list">
                    <li>Predictive Modeling</li>
                    <li>Natural Language Processing</li>
                    <li>Computer Vision</li>
                    <li>TensorFlow & PyTorch</li>
                    <li>MLOps & Model Deployment</li>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-secondary mb-3">⚙️ Backend Development</h4>
                  <ul className="skill-list">
                    <li>Python & frameworks (FastAPI, Django, Flask)</li>
                    <li>RESTful API Design</li>
                    <li>Database Design</li>
                    <li>Authentication & Security</li>
                    <li>Microservices Architecture</li>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-primary mb-3">🎨 Frontend Development</h4>
                  <ul className="skill-list">
                    <li>React.js & Redux</li>
                    <li>JavaScript/TypeScript</li>
                    <li>HTML5 & CSS3</li>
                    <li>Responsive Design</li>
                    <li>UI/UX Principles</li>
                  </ul>
                </div>
              </Col>
            </Row>
          </div>
        </Container>
      </section>

      <section className="section">
        <Container>
          <h2 className="section-title text-center">Professional Experience</h2>
          <div className="section-intro text-center mb-5">
            <p className="lead">
              A track record of delivering impactful solutions across various industries
            </p>
          </div>
          <Row>
            <Col lg={8} className="mx-auto">
              <div className="timeline">
                {experience.map((exp, index) => (
                  <div className="experience-card mb-4" key={index}>
                    <div className="card-body text-left">
                      <div className="experience-header">
                        <h3 className="experience-position">{exp.position}</h3>
                        <div className="experience-duration">
                          {exp.start_date} - {exp.end_date || 'Present'}
                        </div>
                      </div>
                      <div className="experience-company mb-3">
                        <div className="company-logo-container">
                          {/* Use company name to determine which logo to display */}
                          {exp.company.toLowerCase().includes('dfki') && (
                            <img
                              src="https://upload.wikimedia.org/wikipedia/commons/thumb/e/e6/DFKI_Logo.svg/1200px-DFKI_Logo.svg.png"
                              alt={`${exp.company} logo`}
                              className="company-logo"
                            />
                          )}
                          {exp.company.toLowerCase().includes('almedia') && (
                            <img
                              src="https://media.licdn.com/dms/image/v2/C4D0BAQGJKhKJKhKJKg/company-logo_200_200/company-logo_200_200/0/1630511234567/almedia_logo?e=2147483647&v=beta&t=example"
                              alt={`${exp.company} logo`}
                              className="company-logo"
                              onError={(e) => {
                                e.target.src = "https://via.placeholder.com/120x80/333/fff?text=Almedia";
                              }}
                            />
                          )}
                          {exp.company.toLowerCase().includes('citiustech') && (
                            <img
                              src="https://www.citiustech.com/wp-content/uploads/2023/03/CitiusTech-Logo.png"
                              alt={`${exp.company} logo`}
                              className="company-logo"
                            />
                          )}
                          {exp.company.toLowerCase().includes('kpi') && (
                            <img
                              src="https://www.kpipartners.com/wp-content/uploads/2021/03/KPI-Partners-Logo.png"
                              alt={`${exp.company} logo`}
                              className="company-logo"
                            />
                          )}
                          {exp.company.toLowerCase().includes('synechron') && (
                            <img
                              src="https://www.synechron.com/wp-content/uploads/2023/01/Synechron-Logo.png"
                              alt={`${exp.company} logo`}
                              className="company-logo"
                            />
                          )}
                          {(exp.company.toLowerCase().includes('publicis') || exp.company.toLowerCase().includes('sapient')) && (
                            <img
                              src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b1/Publicis_Sapient_logo.svg/1200px-Publicis_Sapient_logo.svg.png"
                              alt={`${exp.company} logo`}
                              className="company-logo"
                            />
                          )}
                          {exp.company.toLowerCase().includes('amigoes') && (
                            <img
                              src="https://via.placeholder.com/120x80/333/fff?text=Amigoes+Webtech"
                              alt={`${exp.company} logo`}
                              className="company-logo"
                            />
                          )}
                        </div>
                        <span className="company-name">{exp.company}</span>
                      </div>
                      <p className="experience-description">{exp.description}</p>
                      <div className="experience-technologies">
                        {exp.technologies.map((tech, i) => (
                          <span className="tech-badge" key={i}>
                            {tech}
                          </span>
                        ))}
                      </div>
                      <div className="experience-achievements mt-3">
                        <h5 className="achievements-title">Key Achievements:</h5>
                        <ul className="achievements-list">
                          {exp.achievements && exp.achievements.map((achievement, i) => (
                            <li key={i}>{achievement}</li>
                          ))}
                          {!exp.achievements && (
                            <>
                              <li>Successfully delivered projects on time and within budget</li>
                              <li>Collaborated effectively with cross-functional teams</li>
                              <li>Implemented best practices and improved development workflows</li>
                            </>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section className="section bg-gradient-dark">
        <Container>
          <h2 className="section-title text-center">Education</h2>
          <div className="section-intro text-center mb-5">
            <p className="lead">
              Academic background and formal education
            </p>
          </div>
          <Row>
            <Col lg={8} className="mx-auto">
              <div className="timeline">
                {education.map((edu, index) => (
                  <div className="education-card mb-4" key={index}>
                    <div className="card-body text-left">
                      <div className="education-header">
                        <h3 className="education-degree">{edu.degree} in {edu.field}</h3>
                        <div className="education-duration">
                          {edu.start_date} - {edu.end_date}
                        </div>
                      </div>
                      <div className="education-institution mb-3">
                        <div className="institution-logo-container">
                          {/* Use institution name to determine which logo to display */}
                          {edu.institution.toLowerCase().includes('ignou') && (
                            <img
                              src="https://upload.wikimedia.org/wikipedia/en/thumb/5/5f/IGNOU_Logo.png/1200px-IGNOU_Logo.png"
                              alt={`${edu.institution} logo`}
                              className="institution-logo"
                            />
                          )}
                          {(edu.institution.toLowerCase().includes('berlin') || edu.institution.toLowerCase().includes('iu') || edu.institution.toLowerCase().includes('international university')) && (
                            <img
                              src="https://upload.wikimedia.org/wikipedia/commons/thumb/8/8a/IU_International_University_of_Applied_Sciences_logo.svg/1200px-IU_International_University_of_Applied_Sciences_logo.svg.png"
                              alt={`${edu.institution} logo`}
                              className="institution-logo"
                            />
                          )}
                          {(edu.institution.toLowerCase().includes('sgbau') || edu.institution.toLowerCase().includes('sant gadge') || edu.institution.toLowerCase().includes('amravati')) && (
                            <img
                              src="https://upload.wikimedia.org/wikipedia/en/4/42/Sant_Gadge_Baba_Amravati_University_logo.png"
                              alt={`${edu.institution} logo`}
                              className="institution-logo"
                            />
                          )}
                        </div>
                        <span className="institution-name">{edu.institution}</span>
                      </div>
                      {edu.description && <p className="education-description">{edu.description}</p>}
                      {edu.achievements && (
                        <div className="education-achievements">
                          <h5 className="achievements-title">Highlights:</h5>
                          <ul className="achievements-list">
                            {edu.achievements.map((achievement, i) => (
                              <li key={i}>{achievement}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </Col>
          </Row>


        </Container>
      </section>
    </div>
  );
};

export default About;
