/* Certifications Page Styles */
.section {
  padding: 4rem 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--md-purple), var(--md-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-intro {
  max-width: 600px;
  margin: 0 auto;
}

.section-intro .lead {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Certifications */
.certification-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.certification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.certification-logo {
  margin-bottom: 1rem;
}

.cert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(187, 134, 252, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-purple);
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.certification-card:hover .cert-icon {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.cert-logo-img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px;
  transition: all 0.3s ease;
  border: 1px solid rgba(187, 134, 252, 0.2);
}

.certification-card:hover .cert-logo-img {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(187, 134, 252, 0.4);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.3);
}

.certification-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.certification-issuer {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.certification-date {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.certification-verification {
  margin-top: 0.5rem;
}

.verification-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--md-purple), var(--md-blue));
  color: white;
  text-decoration: none;
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.verification-link:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(187, 134, 252, 0.4);
}

.verification-unavailable {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(128, 128, 128, 0.1);
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section {
    padding: 2rem 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-intro .lead {
    font-size: 1.1rem;
  }

  .certification-card {
    margin-bottom: 1rem;
  }

  .cert-icon {
    width: 50px;
    height: 50px;
    font-size: 0.9rem;
  }

  .cert-logo-img {
    width: 50px;
    height: 50px;
    padding: 6px;
  }

  .certification-title {
    font-size: 1rem;
  }
}
