.App {
  text-align: center;
}

/* Ensure content doesn't get hidden behind fixed datetime banner and navbar */
main {
  padding-top: 5.5rem;
  min-height: calc(100vh - 5.5rem);
}

/* Exception for home page which handles its own padding */
.home-page main {
  padding-top: 0;
  min-height: 100vh;
}

/* Mobile responsive adjustments */
@media (max-width: 992px) {
  main {
    padding-top: 4.8rem;
    min-height: calc(100vh - 4.8rem);
  }
}

@media (max-width: 768px) {
  main {
    padding-top: 4.2rem;
    min-height: calc(100vh - 4.2rem);
  }
}

/* iPhone and small mobile devices */
@media (max-width: 576px) {
  main {
    padding-top: 3.8rem;
    min-height: calc(100vh - 3.8rem);
  }
}

/* Very small mobile devices */
@media (max-width: 480px) {
  main {
    padding-top: 3.5rem;
    min-height: calc(100vh - 3.5rem);
  }
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
